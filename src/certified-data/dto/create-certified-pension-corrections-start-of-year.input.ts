import { InputType, Field, Float } from '@nestjs/graphql'
import { IsNumber, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedPensionCorrectionsStartOfYearInput {
    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualOldAgePension?: number

    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    attainableGrossAnnualOldAgePension?: number

    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualPartnersPension?: number

    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualSinglesPension?: number

    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    grossAnnualDisabilityPension?: number

    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    extraAccruedGrossAnnualOldAgePension?: number

    @Field(() = 3e Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    extraAccruedGrossAnnualPartnersPension?: number

    @Field(() = 3e [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() = 3e CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}
